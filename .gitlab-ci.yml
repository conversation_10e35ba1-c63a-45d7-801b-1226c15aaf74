
# GitLab CI/CD Pipeline for CDTest Spring Boot Application
image: gradle:jdk24-alpine

# Global variables
variables:
  GRADLE_OPTS: "-Dorg.gradle.daemon=false"
  GRADLE_USER_HOME: "$CI_PROJECT_DIR/.gradle"

# Define pipeline stages
stages:
  - build
  - test
  - deploy

before_script:
  - export GRADLE_USER_HOME

# Build stage - Compile and package the application
build:
  stage: build
  script:
    - echo "Building CDTest application..."
    - gradle --build-cache clean bootJar
    - echo "Build completed successfully"
    - echo "Generated JAR files:"
    - ls -la build/libs/
  cache:
    key: "$CI_COMMIT_REF_NAME"
    policy: push
    paths:
      - build
      - .gradle
  artifacts:
    name: "cdtest-$CI_COMMIT_SHORT_SHA"
    paths:
      - build/libs/*.jar
    reports:
      dotenv: build.env
    expire_in: 30 days
  after_script:
    - echo "JAR_FILE=$(ls build/libs/*.jar | head -1)" > build.env
    - echo "JAR_NAME=$(basename $(ls build/libs/*.jar | head -1))" >> build.env

# Test stage - Run unit tests
test:
  stage: test
  script:
    - echo "Running tests..."
    - gradle check
    - echo "Tests completed successfully"
  cache:
    key: "$CI_COMMIT_REF_NAME"
    policy: pull
    paths:
      - build
      - .gradle
  artifacts:
    when: always
    reports:
      junit:
        - build/test-results/test/TEST-*.xml
    paths:
      - build/reports/tests/test/
    expire_in: 7 days
  coverage: '/Total.*?([0-9]{1,3})%/'

# Publish artifacts job - Make JAR available for download
publish_artifacts:
  stage: deploy
  dependencies:
    - build
  script:
    - echo "Publishing artifacts..."
    - echo "Artifact published successfully"
    - |
      echo "=== Artifact Information ==="
      echo "Commit: $CI_COMMIT_SHA"
      echo "Branch: $CI_COMMIT_REF_NAME"
      echo "JAR File: $(basename $JAR_FILE)"
      echo "File Size: $(du -h $JAR_FILE | cut -f1)"
      echo "==========================="
  artifacts:
    name: "cdtest-jar-$CI_COMMIT_SHORT_SHA"
    paths:
      - build/libs/*.jar
    expire_in: 90 days
  only:
    - main
    - develop
    - tags

# Deploy to production
deploy:
  stage: deploy
  dependencies:
    - build
  script:
    - echo "Deploying CDTest application..."
  environment:
    name: production
    url: https://cdtest.example.com
  when: manual
  only:
    - main
