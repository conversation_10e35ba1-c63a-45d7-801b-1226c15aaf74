<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CDTest - Data Viewer</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #007bff;
            padding-bottom: 10px;
        }
        
        .controls {
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        
        button:hover {
            background-color: #0056b3;
        }
        
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        
        .loading {
            text-align: center;
            color: #666;
            font-style: italic;
            padding: 20px;
        }
        
        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border: 1px solid #f5c6cb;
        }
        
        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border: 1px solid #c3e6cb;
        }
        
        .data-count {
            background-color: #e9ecef;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            font-weight: bold;
            color: #495057;
        }
        
        .data-grid {
            display: grid;
            gap: 15px;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        }
        
        .data-item {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .data-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        
        .data-item h3 {
            margin: 0 0 10px 0;
            color: #007bff;
            font-size: 18px;
        }
        
        .data-item p {
            margin: 5px 0;
            color: #666;
        }
        
        .data-item .id {
            font-family: 'Courier New', monospace;
            background-color: #e9ecef;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
            color: #495057;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }
        
        .empty-state h3 {
            margin-bottom: 10px;
            color: #495057;
        }
        
        .add-data-form {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #dee2e6;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #495057;
        }
        
        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        
        .form-group textarea {
            resize: vertical;
            min-height: 80px;
        }
        
        .form-actions {
            display: flex;
            gap: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>CDTest Data Viewer</h1>
        
        <div class="add-data-form">
            <h3>Add New Data Item</h3>
            <form id="addDataForm">
                <div class="form-group">
                    <label for="name">Name:</label>
                    <input type="text" id="name" name="name" required>
                </div>
                <div class="form-group">
                    <label for="description">Description:</label>
                    <textarea id="description" name="description" required></textarea>
                </div>
                <div class="form-actions">
                    <button type="submit">Add Data Item</button>
                    <button type="button" onclick="clearForm()">Clear</button>
                </div>
            </form>
        </div>
        
        <div class="controls">
            <button onclick="loadData()" id="refreshBtn">Refresh Data</button>
            <span id="lastUpdated"></span>
        </div>
        
        <div id="message"></div>
        <div id="dataCount" class="data-count" style="display: none;"></div>
        <div id="dataContainer">
            <div class="loading">Loading data...</div>
        </div>
    </div>

    <script>
        let dataCache = [];
        
        // Load data when page loads
        document.addEventListener('DOMContentLoaded', function() {
            loadData();
            setupForm();
        });
        
        function setupForm() {
            const form = document.getElementById('addDataForm');
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                addDataItem();
            });
        }
        
        async function loadData() {
            const refreshBtn = document.getElementById('refreshBtn');
            const dataContainer = document.getElementById('dataContainer');
            const messageDiv = document.getElementById('message');
            const dataCountDiv = document.getElementById('dataCount');
            const lastUpdatedSpan = document.getElementById('lastUpdated');
            
            refreshBtn.disabled = true;
            refreshBtn.textContent = 'Loading...';
            dataContainer.innerHTML = '<div class="loading">Loading data...</div>';
            messageDiv.innerHTML = '';
            
            try {
                const response = await fetch('/api/data');
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                dataCache = data;
                
                displayData(data);
                
                // Update data count
                dataCountDiv.textContent = `Total items: ${data.length}`;
                dataCountDiv.style.display = 'block';
                
                // Update last updated time
                lastUpdatedSpan.textContent = `Last updated: ${new Date().toLocaleTimeString()}`;
                
                showMessage('Data loaded successfully!', 'success');
                
            } catch (error) {
                console.error('Error loading data:', error);
                dataContainer.innerHTML = '<div class="error">Failed to load data. Please try again.</div>';
                showMessage(`Error loading data: ${error.message}`, 'error');
                dataCountDiv.style.display = 'none';
            } finally {
                refreshBtn.disabled = false;
                refreshBtn.textContent = 'Refresh Data';
            }
        }
        
        function displayData(data) {
            const dataContainer = document.getElementById('dataContainer');
            
            if (data.length === 0) {
                dataContainer.innerHTML = `
                    <div class="empty-state">
                        <h3>No data found</h3>
                        <p>Add some data items using the form above to see them here.</p>
                    </div>
                `;
                return;
            }
            
            const dataGrid = document.createElement('div');
            dataGrid.className = 'data-grid';
            
            data.forEach(item => {
                const dataItem = document.createElement('div');
                dataItem.className = 'data-item';
                
                dataItem.innerHTML = `
                    <h3>${escapeHtml(item.name || 'Unnamed')}</h3>
                    <p><strong>Description:</strong> ${escapeHtml(item.description || 'No description')}</p>
                    <p><strong>ID:</strong> <span class="id">${escapeHtml(item.id || 'N/A')}</span></p>
                `;
                
                dataGrid.appendChild(dataItem);
            });
            
            dataContainer.innerHTML = '';
            dataContainer.appendChild(dataGrid);
        }
        
        async function addDataItem() {
            const form = document.getElementById('addDataForm');
            const formData = new FormData(form);
            const submitBtn = form.querySelector('button[type="submit"]');
            
            const dataItem = {
                name: formData.get('name').trim(),
                description: formData.get('description').trim()
            };
            
            if (!dataItem.name || !dataItem.description) {
                showMessage('Please fill in all fields', 'error');
                return;
            }
            
            submitBtn.disabled = true;
            submitBtn.textContent = 'Adding...';
            
            try {
                const response = await fetch('/api/data', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(dataItem)
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const savedItem = await response.json();
                showMessage('Data item added successfully!', 'success');
                clearForm();
                loadData(); // Refresh the data display
                
            } catch (error) {
                console.error('Error adding data:', error);
                showMessage(`Error adding data: ${error.message}`, 'error');
            } finally {
                submitBtn.disabled = false;
                submitBtn.textContent = 'Add Data Item';
            }
        }
        
        function clearForm() {
            document.getElementById('addDataForm').reset();
        }
        
        function showMessage(message, type) {
            const messageDiv = document.getElementById('message');
            messageDiv.innerHTML = `<div class="${type}">${escapeHtml(message)}</div>`;
            
            // Auto-hide success messages after 3 seconds
            if (type === 'success') {
                setTimeout(() => {
                    messageDiv.innerHTML = '';
                }, 3000);
            }
        }
        
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
    </script>
</body>
</html>
