spring:
    application:
        name: cdtest
    data:
        mongodb:
            host: localhost
            port: 27017
            database: mydatabase
            username: root
            password: secret
            authentication-database: admin

management:
  endpoints:
    web:
      exposure:
        include: health
  endpoint:
    health:
      show-details: always

springdoc:
  api-docs:
    path: /api-docs
  swagger-ui:
    path: /swagger-ui.html
    operationsSorter: method
