package org.cd.ptm.cdtest.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.cd.ptm.cdtest.model.DataItem;
import org.cd.ptm.cdtest.repository.DataItemRepository;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/data")
@RequiredArgsConstructor
@Slf4j
public class DataController {

    private final DataItemRepository repository;

    @PostMapping
    public ResponseEntity<DataItem> saveData(@RequestBody DataItem dataItem) {
        DataItem savedItem = repository.save(dataItem);
        return new ResponseEntity<>(savedItem, HttpStatus.CREATED);
    }
}