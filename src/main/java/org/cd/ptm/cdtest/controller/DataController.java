package org.cd.ptm.cdtest.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.cd.ptm.cdtest.model.DataItem;
import org.cd.ptm.cdtest.repository.DataItemRepository;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/api/data")
@RequiredArgsConstructor
@Slf4j
public class DataController {

    private final DataItemRepository repository;

    @GetMapping
    public ResponseEntity<List<DataItem>> getAllData() {
        List<DataItem> allItems = repository.findAll();
        log.info("Retrieved {} data items", allItems.size());
        return new ResponseEntity<>(allItems, HttpStatus.OK);
    }

    @PostMapping
    public ResponseEntity<DataItem> saveData(@RequestBody DataItem dataItem) {
        DataItem savedItem = repository.save(dataItem);
        log.info("Data saved successfully: {}", savedItem);
        return new ResponseEntity<>(savedItem, HttpStatus.CREATED);
    }
}