package org.cd.ptm.cdtest.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class OpenApiConfig {

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("CDTest API")
                        .description("Spring Boot REST API for CDTest application")
                        .contact(new Contact().name("PTM").email("<EMAIL>"))
                        .version("1.0.0"));
    }
}
