#!/bin/bash

# JAR validation script for CDTest application
# This script validates the generated JAR file

set -e

echo "🔍 Validating CDTest JAR file..."

# Find the JAR file
JAR_FILE=$(find build/libs -name "*.jar" | head -1)

if [ -z "$JAR_FILE" ]; then
    echo "❌ No JAR file found in build/libs/"
    echo "Run './gradlew bootJar' first to build the application"
    exit 1
fi

echo "📦 Found JAR file: $JAR_FILE"

# Get file information
JAR_NAME=$(basename "$JAR_FILE")
JAR_SIZE=$(du -h "$JAR_FILE" | cut -f1)

echo ""
echo "=== JAR File Information ==="
echo "Name: $JAR_NAME"
echo "Size: $JAR_SIZE"
echo "Path: $JAR_FILE"
echo "=========================="

# Validate JAR structure
echo ""
echo "🔍 Validating JAR structure..."

# Check if it's a valid JAR file
if jar tf "$JAR_FILE" > /dev/null 2>&1; then
    echo "✅ JAR file structure is valid"
else
    echo "❌ JAR file structure is invalid"
    exit 1
fi

# Check for Spring Boot main class
if jar tf "$JAR_FILE" | grep -q "org/springframework/boot/loader/launch/JarLauncher.class"; then
    echo "✅ Spring Boot JAR structure detected"
elif jar tf "$JAR_FILE" | grep -q "org/springframework/boot/loader/JarLauncher.class"; then
    echo "✅ Spring Boot JAR structure detected (legacy)"
else
    echo "⚠️  Not a Spring Boot executable JAR"
fi

# Check for application classes
if jar tf "$JAR_FILE" | grep -q "org/cd/ptm/cdtest/"; then
    echo "✅ Application classes found"
else
    echo "❌ Application classes not found"
    exit 1
fi

# Check for dependencies
if jar tf "$JAR_FILE" | grep -q "BOOT-INF/lib/"; then
    DEP_COUNT=$(jar tf "$JAR_FILE" | grep "BOOT-INF/lib/" | wc -l)
    echo "✅ Dependencies included ($DEP_COUNT files)"
else
    echo "⚠️  No dependencies found in BOOT-INF/lib/"
fi

# Check manifest
echo ""
echo "🔍 Checking JAR manifest..."
MANIFEST=$(unzip -p "$JAR_FILE" META-INF/MANIFEST.MF 2>/dev/null || echo "")

if echo "$MANIFEST" | grep -q "Main-Class:"; then
    MAIN_CLASS=$(echo "$MANIFEST" | grep "Main-Class:" | cut -d' ' -f2- | tr -d '\r')
    echo "✅ Main-Class found: $MAIN_CLASS"
else
    echo "❌ Main-Class not found in manifest"
    exit 1
fi

if echo "$MANIFEST" | grep -q "Start-Class:"; then
    START_CLASS=$(echo "$MANIFEST" | grep "Start-Class:" | cut -d' ' -f2- | tr -d '\r')
    echo "✅ Start-Class found: $START_CLASS"
else
    echo "⚠️  Start-Class not found (may not be a Spring Boot JAR)"
fi

echo ""
echo "🎉 JAR validation completed successfully!"
echo ""
echo "To run the application:"
echo "  java -jar $JAR_FILE"
echo ""
echo "To test the JAR quickly:"
echo "  timeout 10s java -jar $JAR_FILE || echo 'JAR started successfully (timed out after 10s)'"
