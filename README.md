# CDTest

A Spring Boot REST API application for managing data items with MongoDB integration.

## Features

- REST API for data management (GET, POST)
- MongoDB integration
- Web interface for data visualization
- OpenAPI/Swagger documentation
- Health check endpoints
- Docker support
- GitLab CI/CD pipeline

## Getting Started

### Prerequisites

- Java 24 or higher
- MongoDB (or use Docker Compose)
- Gradle (wrapper included)

### Local Development

1. Clone the repository:
```bash
git clone https://gitlab.ecolife.eu.com:2323/Playground/cdtest.git
cd cdtest
```

2. Start MongoDB using Docker Compose:
```bash
docker-compose up -d mongodb
```

3. Run the application:
```bash
./gradlew bootRun
```

4. Access the application:
   - Web Interface: http://localhost:8080
   - API Documentation: http://localhost:8080/swagger-ui.html
   - Health Check: http://localhost:8080/actuator/health

### API Endpoints

- `GET /api/data` - Retrieve all data items
- `POST /api/data` - Create a new data item
- `GET /actuator/health` - Health check endpoint

## CI/CD Pipeline & Artifacts

This project includes a comprehensive GitLab CI/CD pipeline that automatically builds and publishes JAR artifacts.

### Pipeline Stages

1. **Build** - Compiles and packages the application into a JAR file
2. **Test** - Runs unit tests and generates reports
3. **Deploy** - Publishes artifacts and handles deployments

### Artifacts Management

The CI/CD pipeline automatically generates and stores JAR artifacts:

#### Build Artifacts
- **Location**: Available in GitLab under `CI/CD > Pipelines > [Pipeline] > Job artifacts`
- **Retention**: 30 days for build artifacts, 90 days for published artifacts
- **Naming**: `cdtest-[commit-hash].jar`

#### Accessing Artifacts
1. **Via GitLab UI**:
   - Go to your project → CI/CD → Pipelines
   - Click on the pipeline → Click on the `build` or `publish_artifacts` job
   - Download artifacts from the right sidebar

2. **Via GitLab API**:
   ```bash
   # Get latest artifact from main branch
   curl --header "PRIVATE-TOKEN: <your-token>" \
        "https://gitlab.ecolife.eu.com:2323/api/v4/projects/:id/jobs/artifacts/main/download?job=publish_artifacts"
   ```

3. **Direct Download URL**:
   ```
   https://gitlab.ecolife.eu.com:2323/Playground/cdtest/-/jobs/artifacts/main/download?job=publish_artifacts
   ```

#### Local Build
To build artifacts locally:
```bash
# Use the provided script
./scripts/build-local.sh

# Or manually
./gradlew clean bootJar
ls build/libs/
```

#### Artifact Information
Each artifact includes:
- Application JAR file (`cdtest-*.jar`)
- Build metadata (commit hash, branch, file size)
- Test reports (when available)

The JAR files are ready-to-run Spring Boot applications that include all dependencies.

### Running the JAR
Once you have the JAR file (either from CI/CD artifacts or local build):

```bash
# Run the application
java -jar cdtest-*.jar

# Run with custom profile
java -jar cdtest-*.jar --spring.profiles.active=production

# Run with custom port
java -jar cdtest-*.jar --server.port=9090
```
